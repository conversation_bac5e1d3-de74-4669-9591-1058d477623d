"use client";

import React, { useEffect, useState } from "react";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faRedo,
  faSearch,
  faEdit,
  faTrash,
  faTimes,
  faFileExport,
  faFileImport,
  faFileExcel,
  faDownload,
} from "@fortawesome/free-solid-svg-icons";
import { getOrganization, deleteOrganization, saveOrganization } from "@/services/organization/organization";
import { OrganizationItem } from "@/types/organization/organization";
import Pagination from "@/components/common/Pagination";
import { Button, Modal, Form, Input, Select, notification } from "antd";
import { useBreadcrumb } from "@/context/BreadcrumbContext";
import { time } from "node:console";
import { useLoader } from "@/context/LoaderContext";
import * as XLSX from "xlsx";
import AdminLayout from "@/components/Layouts/AdminLayout";

const initialForm: Partial<OrganizationItem> = {
  name: "",
  type: ""
};

export default function OrganizationsPage() {
  const { setLoading } = useLoader();
  const { setBreadcrumb } = useBreadcrumb();
  const [data, setData] = useState<OrganizationItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [inputSearchTerm, setInputSearchTerm] = useState("");
  const [form, setForm] = useState<Partial<OrganizationItem>>(initialForm);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [antdForm] = Form.useForm();
  const [selectedOrganizations, setSelectedOrganizations] = useState<number[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  const [previewImportData, setPreviewImportData] = useState<OrganizationItem[]>([]);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  


  const fetchOrganizations = async () => {
    try {
      setLoading(true);
      const res = await getOrganization({
        SearchTerm: searchTerm,
        Page: currentPage,
        ItemPerPage: pageSize,
      });
      setData(res.data.data?.items || []);
      setTotalItems(res.data.data?.total || 0);
      setSelectedOrganizations([]);
    } catch (err) {
      console.error("Lỗi tải danh sách tổ chức", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setBreadcrumb("Quản lý tổ chức", "Danh sách tổ chức");
  }, []);

  // Reset currentPage về 1 khi searchTerm thay đổi
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Debounce fetchOrganizations khi searchTerm, currentPage hoặc pageSize thay đổi
  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchOrganizations();
    }, 300); // Debounce 300ms

    return () => clearTimeout(timeout); // Clear timeout nếu thay đổi trong thời gian chờ
  }, [searchTerm, currentPage, pageSize]);

 const handleSearch = async (e: React.FormEvent) => {
  e.preventDefault();
   setSearchTerm(inputSearchTerm);
  setCurrentPage(1); // Đặt lại trang đầu tiên

  // Delay gọi fetchOrganizations sau khi setState hoàn tất
  setTimeout(() => {
    fetchOrganizations(); // fetchOrganizations sẽ gọi theo currentPage = 1
  }, 0);
};

  const resetSearch = async () => {
    setSearchTerm("");
    setCurrentPage(1);
    await fetchOrganizations();
  };

  const handleSubmit = async () => {
    try {
      const values = await antdForm.validateFields();
      setLoading(true);
      await saveOrganization(editingId ? { ...values, id: editingId } : values);
      notification.success({
        message: editingId ? "Cập nhật thành công" : "Thêm mới thành công",
      });
      setCurrentPage(1);
      await fetchOrganizations();
      closeModal();
    } catch (errorInfo) {
      console.log("Validation Failed:", errorInfo);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: "Bạn có chắc chắn muốn xoá?",
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          await deleteOrganization(id);
          notification.success({ message: "Xoá thành công" });
          setCurrentPage(1);
          await fetchOrganizations();
        } catch {
          notification.error({ message: "Lỗi khi xoá tổ chức" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const openModal = (Organization?: OrganizationItem) => {
    if (Organization) {
      setForm(Organization);
      setEditingId(Organization.id);
      antdForm.setFieldsValue(Organization); // ✅ Set form field values
    } else {
      setForm(initialForm);
      setEditingId(null);
      antdForm.resetFields(); // ✅ Reset fields
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setForm(initialForm);
    setEditingId(null);
    setIsModalOpen(false);
  };

  const handleDeleteMultiple = async () => {
    Modal.confirm({
      title: `Bạn có chắc chắn muốn xoá ${selectedOrganizations.length} tổ chức?`,
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          for (const id of selectedOrganizations) {
            await deleteOrganization(id);
          }
          notification.success({ message: "Xoá thành công" });
          setSelectedOrganizations([]);
          setCurrentPage(1);
          await fetchOrganizations();
        } catch {
          notification.error({ message: "Lỗi khi xoá nhiều tổ chức" });
        } finally {
          setLoading(false);
        }
      },
    });
  };


  const handleExportExcel = () => {
    const exportData = data.map(({ name, type }) => ({
    name,type
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "DanhSachToChuc");

    XLSX.writeFile(workbook, "danh_sach_to_chuc.xlsx");
  };


  return (
    <AdminLayout>
      <div className="font-sans mx-auto rounded-md bg-white p-6">
        {/* Search + Add Form */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <form className="flex w-full sm:w-auto" onSubmit={handleSearch}>
            <div className="relative flex-grow">
              <input
                id="search"
                type="search"
                value={searchTerm}
                onChange={(e) => setInputSearchTerm(e.target.value)}
                placeholder="Tìm kiếm theo tên"
                className="w-full rounded-l-md border border-gray-300 py-2 pl-10 pr-12 text-gray-400 placeholder-gray-400 focus:border-red-600 focus:outline-none focus:ring-2 focus:ring-red-600"
              />
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-lg text-gray-400">
                <FontAwesomeIcon icon={faSearch} />
              </span>
              <button
                type="reset"
                onClick={resetSearch}
                className="absolute right-0 top-1/2 -translate-y-1/2 rounded-r-md border-l border-gray-300 bg-white px-3 py-2 text-gray-600 hover:text-gray-900"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            <button
              type="submit"
              className="ml-2 flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </form>

          <div className="flex items-end gap-2">
            {selectedOrganizations.length > 0 && (
              <>
                <Button
                  danger
                  onClick={handleDeleteMultiple}
                  className="rounded-none"
                >
                  Xoá đã chọn ({selectedOrganizations.length})
                </Button>
              </>
            )}

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => openModal()}
            >
              Thêm tổ chức
            </Button>
            
            <Button
              className="rounded-none  bg-success text-white"
              onClick={handleExportExcel}
              icon={<FontAwesomeIcon icon={faDownload} />}
            >
              Xuất Excel
            </Button>
          </div>
        </div>

        {/* Table */}
        <table className="w-full table-auto border-collapse text-sm">
          <thead>
            <tr className="bg-gray-200 font-semibold text-gray-900">
              <th className="w-[60px] border px-2 py-3 text-center">
                <div className="flex items-center justify-center gap-1">
                  <input
                    type="checkbox"
                    checked={selectedOrganizations.length === data.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedOrganizations(data.map((item) => item.id));
                      } else {
                        setSelectedOrganizations([]);
                      }
                    }}
                    className="accent-red-600"
                  />
                </div>
              </th>
              <th className="border px-4 py-3">Tên tổ chức</th>
              <th className="border px-4 py-3">Loại</th>
              <th className="w-[100px] border px-2 py-3 text-right">
                Chức năng
              </th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr
                key={item.id}
                className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-red-50`}
              >
                <td className="w-[60px] border px-2 py-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className="mr-4 text-xs font-bold">
                      {(currentPage - 1) * pageSize + index + 1}
                    </span>
                    <input
                      type="checkbox"
                      checked={selectedOrganizations.includes(item.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedOrganizations([...selectedOrganizations, item.id]);
                        } else {
                          setSelectedOrganizations(
                            selectedOrganizations.filter((id) => id !== item.id),
                          );
                        }
                      }}
                      className="accent-red-600"
                    />
                  </div>
                </td>
                <td className="border px-4 py-3 font-semibold">{item.name}</td>
                <td className="border px-4 py-3">{item.type}</td>
                <td className="border px-2 py-3 text-right">
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => openModal(item)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Sửa"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Xoá"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <Pagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={(page) => setCurrentPage(page)}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setCurrentPage(1); // reset về trang 1 khi đổi size
          }}
        />
      </div>

      <Modal
        title={editingId ? "Cập nhật tổ chức" : "Thêm tổ chức"}
        open={isModalOpen}
        onCancel={closeModal}
        onOk={handleSubmit}
        okText={editingId ? "Cập nhật" : "Thêm mới"}
      >
        <Form layout="vertical" form={antdForm}>
          <Form.Item
            label="Tên tổ chức"
            name="name"
            rules={[{ required: true, message: "Vui lòng nhập tên tổ chức" }]}
          >
            <Input placeholder="Nhập tên tổ chức" />
          </Form.Item>


          <Form.Item
            label="Loại tổ chức"
            name="type"
            rules={[{ required: true, message: "Vui lòng chọn loại tổ chức" }]}
          >
            <Select placeholder="Chọn loại tổ chức">
              <Select.Option value="Trường">Trường</Select.Option>
              <Select.Option value="Trung tâm">Trung tâm</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </AdminLayout>
  );
}
