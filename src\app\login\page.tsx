'use client';

import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import { login } from '@/services/auth';

// Zod schema
const loginSchema = z.object({
  username: z.string().min(1, 'Tên đăng nhập không được để trống'),
  password: z.string().min(1, 'Mật khẩu không được để trống'),
  remember: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: 'admin',
      password: 'Abc@123456',
      remember: false,
    },
  });

  const onSubmit = async (values: LoginFormData) => {
    try {
      const res = await login({
        username: values.username,
        password: values.password,
        isSetCookie: false,
      });

      const tokenData = res.data?.data;
      if (!tokenData?.token || !tokenData?.refreshToken) {
        throw new Error('Không nhận được token từ máy chủ');
      }

      const { token: accessToken, refreshToken } = tokenData;

      if (values.remember) {
        Cookies.set('accessToken', accessToken, { expires: 7 });
        Cookies.set('refreshToken', refreshToken, { expires: 7 });
        sessionStorage.setItem('remember', 'true');
      } else {
        Cookies.set('accessToken', accessToken);
        Cookies.set('refreshToken', refreshToken);
        sessionStorage.setItem('remember', 'false');
      }

      window.location.href = '/';
    } catch (err) {
      console.error('Đăng nhập thất bại:', err);
      setError('username', { message: 'Sai thông tin đăng nhập' });
      setError('password', { message: 'Sai thông tin đăng nhập' });
    }
  };

  return (
    <div
      className="min-h-screen flex flex-col md:flex-row items-center justify-center px-6 md:px-20 py-10 relative"
      style={{ backgroundColor: '#e9083a' }}
    >
      {/* Left side */}
      <div
        className="relative bg-white bg-opacity-20 rounded-2xl p-8 md:p-12 max-w-md w-full text-center md:text-left mb-10 md:mb-0"
        style={{ backdropFilter: 'blur(100px)' }}
      >
        <div className="absolute top-6 left-6 border-t-4 border-l-4 border-white rounded-tl-lg w-16 h-16 pointer-events-none" />
        <div className="absolute bottom-6 right-6 border-b-4 border-r-4 border-white rounded-br-lg w-16 h-16 pointer-events-none" />
        <h2 className="text-white font-extrabold text-lg md:text-xl leading-tight mb-8">
          QUẢN TRỊ PHẦN MỀM HỖ TRỢ <br /> ÔN TẬP CHO HỌC SINH TIỂU HỌC
        </h2>
        <img src="/bg_login.png" alt="Illustration" width={400} height={200} className="mx-auto" />
      </div>

      {/* Right side */}
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="bg-white rounded-xl p-8 w-full max-w-md shadow-lg space-y-5 ml-10"
        autoComplete="off"
      >
        <div className="flex items-center mb-6">
          <img src="/LOGO_VIETIQ.png" alt="Logo" width={100} height={75} className="ml-1" />
        </div>

        <h3 className="text-gray-800 font-extrabold text-xl mb-4 text-center">Đăng nhập</h3>

        {/* Username */}
        <div>
          <label className="block mb-1 font-medium text-sm">
            Tên đăng nhập <span className="text-red-600">*</span>
          </label>
          <input
            {...register('username')}
            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring focus:border-blue-300"
            placeholder="Tên đăng nhập"
          />
          {errors.username && <p className="text-sm text-red-600 mt-1">{errors.username.message}</p>}
        </div>

        {/* Password */}
        <div>
          <label className="block mb-1 font-medium text-sm">
            Mật khẩu <span className="text-red-600">*</span>
          </label>
          <input
            type="password"
            {...register('password')}
            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring focus:border-blue-300"
            placeholder="Mật khẩu"
          />
          {errors.password && <p className="text-sm text-red-600 mt-1">{errors.password.message}</p>}
        </div>

        {/* Remember */}
        <div className="flex items-center space-x-2">
          <input type="checkbox" id="remember" {...register('remember')} />
          <label htmlFor="remember" className="text-sm">
            Lưu thông tin
          </label>
        </div>

        <div className="text-right text-sm">
          <a href="#" className="hover:underline text-gray-600">
            Quên mật khẩu?
          </a>
        </div>

        <button
          type="submit"
          className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-2 rounded-md"
        >
          <i className="fas fa-sign-in-alt mr-2" />
          Đăng nhập
        </button>
      </form>

      {/* Bottom left */}
      <div className="absolute bottom-6 left-6 flex items-center space-x-3 text-white text-sm font-semibold">
        <i className="fas fa-headphones-alt text-xl" />
        <div>
          <div>Liên hệ CSKH</div>
          <div>0203.XXX.XXX</div>
        </div>
      </div>

      {/* SVG */}
      <svg
        aria-hidden="true"
        className="pointer-events-none absolute inset-0 w-full h-full"
        preserveAspectRatio="none"
        viewBox="0 0 1440 320"
        style={{ opacity: 0.15 }}
      >
        <path
          d="M0,96 C144,160 288,32 432,64 C576,96 720,224 864,192 C1008,160 1152,32 1296,64 C1440,96 1584,160 1728,128"
          fill="none"
          stroke="white"
          strokeWidth="3"
          transform="translate(0, 80)"
        />
        <path
          d="M0,128 C144,192 288,64 432,96 C576,128 720,256 864,224 C1008,192 1152,64 1296,96 C1440,128 1584,192 1728,160"
          fill="none"
          stroke="white"
          strokeWidth="3"
          transform="translate(0, 160)"
        />
      </svg>
    </div>
  );
}
