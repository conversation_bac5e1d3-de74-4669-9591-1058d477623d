"use client";

import React, { useEffect, useState } from "react";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faRedo,
  faSearch,
  faEdit,
  faTrash,
  faTimes,
  faFileExport,
  faDownload,
  faSortUp,
  faSortDown,
  faSort,
} from "@fortawesome/free-solid-svg-icons";
import { getClass, deleteClass, saveClass } from "@/services/class/class";
import { ClassItem } from "@/types/class/class";
import Pagination from "@/components/common/Pagination";
import { Button, Modal, Form, Input, Select, notification } from "antd";
import { useBreadcrumb } from "@/context/BreadcrumbContext";
import { time } from "node:console";
import { useLoader } from "@/context/LoaderContext";
import * as XLSX from "xlsx";
import AdminLayout from "@/components/Layouts/AdminLayout";
import { getOrganizationById } from "@/services/organization/organization";
import { OrganizationItem } from "@/types/organization/organization";
import { getOrganization } from "@/services/organization/organization";

const initialForm: Partial<ClassItem> = {
  organizationId: 0,
  name: "",
  academicYear: 0,
};

export default function ClasssPage() {
  const { setLoading } = useLoader();
  const { setBreadcrumb } = useBreadcrumb();
  const [data, setData] = useState<ClassItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [inputSearchTerm, setInputSearchTerm] = useState("");
  const [form, setForm] = useState<Partial<ClassItem>>(initialForm);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [antdForm] = Form.useForm();
  const [selectedClasss, setSelectedClasss] = useState<number[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  const [previewImportData, setPreviewImportData] = useState<ClassItem[]>([]);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  const [searchColumn, setSearchColumn] = useState<string | undefined>();
  const [selectedOrganization, setSelectedOrganization] = useState<number | null>(null);
  const [organizationOptions, setOrganizationOptions] = useState<OrganizationItem[]>([]);

  const [orderColumn, setOrderColumn] = useState<string | undefined>();
  const [orderDirection, setOrderDirection] = useState<0 | 1 | null>(0); // 0: ASC, 1: DESC

  const fetchClasss = async () => {
    try {
      setLoading(true);

      const res = await getClass({
        Page: currentPage,
        ItemPerPage: pageSize,
        SearchTerm: searchTerm,
        SearchColumn: searchColumn,
        OrderColumn: orderColumn,
        Direction: orderDirection,
      });

      const items = res.data.data?.items || [];
      const organizationCache = new Map<number, any>();

      const itemsWithOrganizations = await Promise.all(
        items.map(async (Class) => {
          if (!organizationCache.has(Class.organizationId)) {
            try {
              const organizationRes = await getOrganizationById(
                Class.organizationId,
              );
              organizationCache.set(
                Class.organizationId,
                organizationRes.data.data,
              );
            } catch (err) {
              console.error(
                `Không lấy được Tổ chức - Trường ID ${Class.organizationId}`,
                err,
              );
              organizationCache.set(Class.organizationId, null);
            }
          }

          return {
            ...Class,
            organization: organizationCache.get(Class.organizationId),
          };
        }),
      );

      setData(itemsWithOrganizations);
      setTotalItems(res.data.data?.total || 0);
      setSelectedClasss([]);
    } catch (err) {
      console.error("Lỗi tải danh sách Lớp", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setBreadcrumb("Quản lý Lớp", "Danh sách Lớp");
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (inputSearchTerm.trim()) {
        setSearchColumn("name");
        setSearchTerm(inputSearchTerm.trim());
      } else {
        setSearchColumn(undefined);
        setSearchTerm("");
      }
      setCurrentPage(1);
    }, 500); // debounce 500ms

    return () => clearTimeout(timeout);
  }, [inputSearchTerm]);

  // Reset currentPage về 1 khi searchTerm thay đổi
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Debounce fetchClasss khi searchTerm, currentPage hoặc pageSize thay đổi
  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchClasss();
    }, 300);

    return () => clearTimeout(timeout);
  }, [searchTerm, currentPage, pageSize, orderColumn, orderDirection]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    const isFilteringByOrg = selectedOrganization !== null;

    setSearchTerm(
      isFilteringByOrg ? String(selectedOrganization) : inputSearchTerm,
    );
    setSearchColumn(isFilteringByOrg ? "organizationId" : undefined);
    setCurrentPage(1);

    setTimeout(() => {
      fetchClasss();
    }, 0);
  };

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const res = await getOrganization({ ItemPerPage: 999 }); // hàm trả về danh sách organizationItem[]
        console.log("res organization", res);
        setOrganizationOptions(res.data?.data?.items ?? []);
      } catch (err) {
        console.error("Lỗi lấy danh sách Tổ chức - Trường", err);
      }
    };
    fetchOrganizations();
  }, []);

  const resetSearch = async () => {
    setSearchTerm("");
    setInputSearchTerm("");
    setSearchColumn(undefined);
    setSelectedOrganization(null);
    setCurrentPage(1);
    await fetchClasss();
  };

  const handleSubmit = async () => {
    try {
      const values = await antdForm.validateFields();
      setLoading(true);
      await saveClass(
        editingId
          ? {
              ...values,
              id: editingId,
              academicYear: Number(values.academicYear),
              organizationId: Number(values.organizationId),
            }
          : {
              ...values,
              academicYear: Number(values.academicYear),
              organizationId: Number(values.organizationId),
            },
      );
      setCurrentPage(1);
      await fetchClasss();
      closeModal();
    } catch (errorInfo) {
      console.log("Validation Failed:", errorInfo);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: "Bạn có chắc chắn muốn xoá?",
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          await deleteClass(id);
          notification.success({ message: "Xoá thành công" });
          setCurrentPage(1);
          await fetchClasss();
        } catch {
          notification.error({ message: "Lỗi khi xoá Lớp" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const openModal = (Class?: ClassItem) => {
    if (Class) {
      setForm(Class);
      setEditingId(Class.id);
      antdForm.setFieldsValue(Class);
    } else {
      setForm(initialForm);
      setEditingId(null);
      antdForm.resetFields();
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setForm(initialForm);
    setEditingId(null);
    setIsModalOpen(false);
  };

  const handleDeleteMultiple = async () => {
    Modal.confirm({
      title: `Bạn có chắc chắn muốn xoá ${selectedClasss.length} Lớp?`,
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          for (const id of selectedClasss) {
            await deleteClass(id);
          }
          notification.success({ message: "Xoá thành công" });
          setSelectedClasss([]);
          setCurrentPage(1);
          await fetchClasss();
        } catch {
          notification.error({ message: "Lỗi khi xoá nhiều Lớp" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleExportExcel = () => {
    const exportData = data.map(({ organizationId, name, academicYear }) => ({
      organizationId,
      name,
      academicYear,
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "DanhSachTuan");

    XLSX.writeFile(workbook, "danh_sach_tuan.xlsx");
  };

  const handleRemovePreviewItem = (index: number) => {
    setPreviewImportData((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSort = (column: string) => {
    console.log("column", column);
    console.log("orderColumn", orderColumn);
    if (orderColumn === column) {
      setOrderDirection(orderDirection === 0 ? 1 : 0);
    } else {
      setOrderColumn(column);
      setOrderDirection(0); // default ASC
    }
  };

  return (
    <AdminLayout>
      <div className="font-sans mx-auto rounded-md bg-white p-6">
        {/* Search + Add Form */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <form
            className="flex w-full flex-wrap items-center gap-2 sm:w-auto"
            onSubmit={handleSearch}
          >
           <Select
              className="min-w-[220px] [&_.ant-select-selector]:rounded-none"
              allowClear
              placeholder="Chọn Tổ chức - Trường"
              value={selectedOrganization ?? undefined}
              onChange={(value) => {
                setSelectedOrganization(value || null);
                setSearchColumn(value ? "organizationId" : undefined);
                setSearchTerm(value ? String(value) : "");
                setCurrentPage(1);
                fetchClasss(); // GỌI HÀM TÌM KIẾM NGAY SAU KHI CHỌN
              }}
              options={organizationOptions.map((org) => ({
                label: org.name,
                value: org.id,
              }))}
            />

            <div className="relative min-w-[220px] max-w-[300px] flex-grow">
              <input
                id="search"
                type="search"
                value={inputSearchTerm}
                onChange={(e) => setInputSearchTerm(e.target.value)}
                placeholder="Tìm kiếm theo tên"
                className="w-full rounded-md border border-gray-300 py-1 pl-10 pr-12 text-gray-700 placeholder-gray-400 focus:border-red-600 focus:outline-none focus:ring-2 focus:ring-red-600"
              />
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-lg text-gray-400">
                <FontAwesomeIcon icon={faSearch} />
              </span>
              <button
                type="reset"
                onClick={resetSearch}
                className="absolute right-0 top-1/2 -translate-y-1/2 px-3 py-1 text-gray-600 hover:text-gray-900"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            <button
              type="submit"
              className="flex items-center justify-center bg-red-600 p-2 px-4 py-2 text-lg text-white hover:bg-red-700"
            >
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </form>

          <div className="flex flex-wrap items-center gap-2">
            {selectedClasss.length > 0 && (
              <Button
                danger
                onClick={handleDeleteMultiple}
                className="rounded-none"
              >
                Xoá đã chọn ({selectedClasss.length})
              </Button>
            )}

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => openModal()}
            >
              Thêm Lớp
            </Button>

            <Button
              className="rounded-none bg-success text-white"
              onClick={handleExportExcel}
              icon={<FontAwesomeIcon icon={faDownload} />}
            >
              Xuất Excel
            </Button>
          </div>
        </div>

        {/* Table */}
        <table className="w-full table-auto border-collapse text-sm">
          <thead>
            <tr className="bg-gray-200 font-semibold text-gray-900">
              <th className="w-[60px] border px-2 py-3 text-center">
                <div className="flex items-center justify-center gap-1">
                  <input
                    type="checkbox"
                    checked={
                      selectedClasss.length === data.length && data.length > 0
                    }
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedClasss(data.map((item) => item.id));
                      } else {
                        setSelectedClasss([]);
                      }
                    }}
                    className="accent-red-600"
                  />
                </div>
              </th>
              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("organizationId")}
              >
                <div className="flex items-center gap-1">
                  Trường - Tổ chức
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "organizationId"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "organizationId" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>
              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("code")}
              >
                <div className="flex items-center gap-1">
                  Mã Lớp
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "code"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "code" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>
              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("name")}
              >
                <div className="flex items-center gap-1">
                  Tên Lớp
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "name"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "name" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>

              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("academicYear")}
              >
                <div className="flex items-center gap-1">
                  Năm học
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "academicYear"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "academicYear" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>

              <th className="w-[100px] border px-2 py-3 text-right">
                Chức năng
              </th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr
                key={item.id}
                className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-red-50`}
              >
                <td className="w-[60px] border px-2 py-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className="mr-4 text-xs font-bold">
                      {(currentPage - 1) * pageSize + index + 1}
                    </span>
                    <input
                      type="checkbox"
                      checked={selectedClasss.includes(item.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedClasss([...selectedClasss, item.id]);
                        } else {
                          setSelectedClasss(
                            selectedClasss.filter((id) => id !== item.id),
                          );
                        }
                      }}
                      className="accent-red-600"
                    />
                  </div>
                </td>
                <td className="border px-4 py-3 font-semibold">
                  {item.organization?.name}
                </td>
                <td className="border px-4 py-3 font-semibold">{item.code}</td>
                <td className="border px-4 py-3 font-semibold">{item.name}</td>
                <td className="border px-4 py-3">{item.academicYear}</td>
                <td className="border px-2 py-3 text-right">
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => openModal(item)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Sửa"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Xoá"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <Pagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={(page) => setCurrentPage(page)}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setCurrentPage(1); // reset về trang 1 khi đổi size
          }}
        />
      </div>
      <Modal
        title={editingId ? "Cập nhật Lớp" : "Thêm Lớp"}
        open={isModalOpen}
        onCancel={closeModal}
        onOk={handleSubmit}
        okText={editingId ? "Cập nhật" : "Thêm mới"}
      >
        <Form layout="vertical" form={antdForm}>
          <Form.Item
            label="Tổ chức - Trường"
            name="organizationId"
            rules={[{ required: true, message: "Tổ chức - Trường" }]}
          >
            <Select placeholder="Chọn Tổ chức - Trường">
              {organizationOptions.map((organization) => (
                <Select.Option key={organization.id} value={organization.id}>
                  {organization.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label="Mã Lớp"
            name="code"
            rules={[{ required: true, message: "Vui lòng nhập mã Lớp" }]}
          >
            <Input placeholder="Nhập tên Lớp" />
          </Form.Item>
          <Form.Item
            label="Tên Lớp"
            name="name"
            rules={[{ required: true, message: "Vui lòng nhập tên Lớp" }]}
          >
            <Input placeholder="Nhập tên Lớp" />
          </Form.Item>

          <Form.Item
            label="Năm học"
            name="academicYear"
            rules={[{ required: true, message: "Vui lòng nhập năm học" }]}
          >
            <Input type="number" placeholder="Nhập năm học" />
          </Form.Item>
        </Form>
      </Modal>{" "}
    </AdminLayout>
  );
}
