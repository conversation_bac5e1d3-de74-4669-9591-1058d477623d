"use client";

import React, { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faRedo,
  faSearch,
  faEllipsisV,
  faPhoneAlt,
  faSortUp,
  faSortDown,
  faSort,
  faTimes,
  faEdit,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";
import {
  getUser,
  deleteUser,
  createUser,
  updateUser,
} from "@/services/user/user";
import { CreateUser, UserItem } from "@/types/user/user";
import Pagination from "@/components/common/Pagination";
import {
  Button,
  Modal,
  Form,
  Input,
  Select,
  notification,
  Upload,
  Col,
  Row,
  Tag,
  Dropdown,
  Menu,
  message,
} from "antd";
import { useBreadcrumb } from "@/context/BreadcrumbContext";
import { useLoader } from "@/context/LoaderContext";
import * as XLSX from "xlsx";
import AdminLayout from "@/components/Layouts/AdminLayout";
import {
  getOrganizationById,
  getOrganization,
  getAllOrganization,
} from "@/services/organization/organization";
import { OrganizationItem } from "@/types/organization/organization";
import { getAllRoles, getGrantedRolesByRoleId } from "@/services/role/role";
import { getClass } from "@/services/class/class";
import { RoleItem } from "@/types/role/role";
import { ClassItem } from "@/types/class/class";

// Extended UserItem type to include new fields
interface ExtendedUserItem extends UserItem {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  passwordChanged: boolean;
  createdAt: string;
  lastLogin: string | null;
  className?: string;
  organization?: OrganizationItem;
  roles?: string[]; // tên các quyền như ["Admin", "Teacher"]
}

const initialForm: Partial<ExtendedUserItem> = {
  userName: "",
  firstName: "",
  lastName: "",
  email: "",
  phoneNumber: "",
  passwordChanged: false,
  createdAt: "",
  lastLogin: null,
  organizationId: null,
  classId: null,
  className: "",
};

export default function UsersPage() {
  const { setLoading } = useLoader();
  const { setBreadcrumb } = useBreadcrumb();
  const [data, setData] = useState<ExtendedUserItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [inputSearchTerm, setInputSearchTerm] = useState("");
  const [form, setForm] = useState<Partial<ExtendedUserItem>>(initialForm);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [antdForm] = Form.useForm();
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedOrganization, setSelectedOrganization] = useState<
    number | null
  >(null);
  const [selectedRole, setSelectedRole] = useState<number | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedClass, setSelectedClass] = useState<string | null>(null);
  const [organizationOptions, setOrganizationOptions] = useState<
    OrganizationItem[]
  >([]);
  const [orderColumn, setOrderColumn] = useState<string | undefined>();
  const [orderDirection, setOrderDirection] = useState<0 | 1 | null>(0);
  const [allRoles, setAllRoles] = useState<RoleItem[]>([]);
  const [allClasses, setAllClasses] = useState<ClassItem[]>([]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const res = await getUser({
        Page: currentPage,
        ItemPerPage: pageSize,
        SearchTerm: searchTerm,
        SearchColumn: selectedOrganization
          ? "organizationId"
          : selectedRole
            ? "role"
            : selectedStatus
              ? "status"
              : selectedClass
                ? "classId"
                : "name",
        OrderColumn: orderColumn,
        Direction: orderDirection,
      });

      const items = res.data.data?.items || [];
      console.log("items", items);
      const organizationCache = new Map<number, OrganizationItem>();

      const extendedItems: ExtendedUserItem[] = items.map((item) => {
        const org = organizationOptions.find(
          (org) => org.id === item.organizationId,
        );
        const className =
          allClasses.find((c) => c.id === item.classId)?.name || "";

        return {
          ...item,
          passwordChanged: false,
          createdAt: item.createdTime || "",
          lastLogin: null,
          className,
          organization: org,
        };
      });

      setData(extendedItems);
      fetchRolesForUsers(extendedItems);

      setTotalItems(res.data.data?.total || 0);
      setSelectedUsers([]);
    } catch (err) {
      console.error("Lỗi tải danh sách User", err);
    } finally {
      setLoading(false);
    }
  };

  const fetchRolesForUsers = async (users: ExtendedUserItem[]) => {
    const updatedUsers = await Promise.all(
      users.map(async (user) => {
        try {
          const res = await getGrantedRolesByRoleId(user.id); // nếu user.id cũng là roleId
          const roles = res.data.data?.map((r) => r.roleName); // hoặc .code
          return { ...user, roles };
        } catch {
          return { ...user, roles: [] };
        }
      }),
    );
    setData(updatedUsers);
  };

  const fetchAllClasses = async () => {
    try {
      const res = await getClass({});
      setAllClasses(res.data.data?.items||[]);
    } catch (err) {
      console.error("Lỗi khi tải vai trò", err);
    }
  };

  const fetchAllRoles = async () => {
    try {
      const res = await getAllRoles(); // giả sử hàm này gọi đến API lấy danh sách roles
      setAllRoles(res.data.data || []); // đảm bảo `data.data` là RoleItem[]
    } catch (err) {
      console.error("Lỗi khi tải vai trò", err);
    }
  };

  useEffect(() => {
    fetchAllRoles();
    fetchAllClasses();

  }, []);

  useEffect(() => {
    setBreadcrumb("Quản lý User", "Danh sách User");
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (inputSearchTerm.trim()) {
        // setSearchColumn("name");
        setSearchTerm(inputSearchTerm.trim());
      } else if (selectedOrganization) {
        // setSearchColumn("organizationId");
        setSearchTerm(String(selectedOrganization));
      } else if (selectedRole) {
        // setSearchColumn("role");
        setSearchTerm(selectedRole as unknown as string);
      } else if (selectedStatus) {
        // setSearchColumn("status");
        setSearchTerm(selectedStatus);
      } else if (selectedClass) {
        // setSearchColumn("classId");
        setSearchTerm(selectedClass);
      } else {
        // setSearchColumn(undefined);
        setSearchTerm("");
      }
      setCurrentPage(1);
    }, 500);

    return () => clearTimeout(timeout);
  }, [
    inputSearchTerm,
    selectedOrganization,
    selectedRole,
    selectedStatus,
    selectedClass,
  ]);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchUsers();
    }, 300);

    return () => clearTimeout(timeout);
  }, [searchTerm, currentPage, pageSize, orderColumn, orderDirection]);

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const res = await getAllOrganization();
        setOrganizationOptions(res.data?.data ?? []);
      } catch (err) {
        console.error("Lỗi lấy danh sách Tổ chức - Trường", err);
      }
    };
    fetchOrganizations();
  }, []);

  const resetSearch = async () => {
    setSearchTerm("");
    setInputSearchTerm("");
    // setSearchColumn(undefined);
    setSelectedOrganization(null);
    setSelectedRole(null);
    setSelectedStatus(null);
    setSelectedClass(null);
    setCurrentPage(1);
    await fetchUsers();
  };

 const handleCreateSubmit = async () => {
  try {
    const values = await antdForm.validateFields();
    setLoading(true);

    const payload: CreateUser = {
      userName: values.userName,
      email: values.email,
      password: values.password,
      phoneNumber: values.phoneNumber,
      firstName: values.firstName,
      lastName: values.lastName,
      address: values.address,
      imageUrl: values.imageUrl,
      roleNames: values.roleNames,
      organizationId: Number(values.organizationId),
      classId: Number(values.classId),
    };

    const response = await createUser(payload); // 👈 nhận response
    if (response?.data.isSuccess) {
      message.success("Tạo người dùng thành công!");
      setCurrentPage(1);
      await fetchUsers();
      closeModal();
    } else {
      message.error(response?.data.message || "Tạo người dùng thất bại!");
    }
  } catch (errorInfo) {
    console.log("Validation Failed:", errorInfo);
  } finally {
    setLoading(false);
  }
};


const handleUpdateSubmit = async () => {
  try {
    const values = await antdForm.validateFields();
    setLoading(true);

    const payload = {
      phoneNumber: values.phoneNumber,
      firstName: values.firstName,
      lastName: values.lastName,
      address: values.address,
      organizationId: Number(values.organizationId),
      classId: Number(values.classId),
      roleNames: values.roleNames,
    };

    const response = await updateUser(editingId!, payload); // 👈
    if (response?.data.isSuccess) {
      message.success("Cập nhật người dùng thành công!");
      setCurrentPage(1);
      await fetchUsers();
      closeModal();
    } else {
      message.error(response?.data.message || "Cập nhật người dùng thất bại!");
    }
  } catch (errorInfo) {
    console.log("Validation Failed:", errorInfo);
  } finally {
    setLoading(false);
  }
};

  const handleDelete = (item: ExtendedUserItem) => {
    Modal.confirm({
      title: "Xác nhận xóa",
      content: `Bạn có chắc muốn xóa user "${item.userName}" không?`,
      okText: "Xóa",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await deleteUser(item.id);
          fetchUsers(); // reload
          message.success("Đã xóa thành công!");
        } catch (err) {
          message.error("Xóa thất bại!");
        }
      },
    });
  };

  const openModal = (user?: ExtendedUserItem) => {
    if (user) {
      setForm(user);
      setEditingId(user.id);
      antdForm.setFieldsValue(user);
    } else {
      setForm(initialForm);
      setEditingId(null);
      antdForm.resetFields();
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setForm(initialForm);
    setEditingId(null);
    setIsModalOpen(false);
  };

  const handleDeleteMultiple = async () => {
    Modal.confirm({
      title: `Bạn có chắc chắn muốn xoá ${selectedUsers.length} User?`,
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          for (const id of selectedUsers) {
            await deleteUser(id);
          }
          notification.success({ message: "Xoá thành công" });
          setSelectedUsers([]);
          setCurrentPage(1);
          await fetchUsers();
        } catch {
          notification.error({ message: "Lỗi khi xoá nhiều User" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleSort = (column: string) => {
    if (orderColumn === column) {
      setOrderDirection(orderDirection === 0 ? 1 : 0);
    } else {
      setOrderColumn(column);
      setOrderDirection(0);
    }
  };

  return (
    <AdminLayout>
      <div className="font-sans mx-auto rounded-md p-4 sm:p-6">
        <section
          className="mb-6 max-w-full rounded-lg border border-gray-200 bg-white p-6"
          aria-label="Search and filter users"
        >
          <h2 className="mb-1 text-lg font-semibold text-gray-900">
            Tìm kiếm và lọc
          </h2>
          <p className="mb-5 text-sm text-gray-500">
            Tìm kiếm và lọc người dùng theo tiêu chí
          </p>
          <form className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
            <div className="relative max-w-full flex-1 sm:max-w-xs">
              <Input
                id="search"
                type="search"
                value={inputSearchTerm}
                onChange={(e) => setInputSearchTerm(e.target.value)}
                placeholder="Tìm kiếm theo tên, số điện thoại, email..."
                className="w-full rounded-xl border border-gray-300 py-2 pl-10 pr-3 text-sm text-gray-700 placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="pointer-events-none absolute inset-y-0 left-3 flex items-center rounded-xl text-gray-400">
                <FontAwesomeIcon icon={faSearch} />
              </span>
              {inputSearchTerm && (
                <button
                  type="button"
                  onClick={resetSearch}
                  className="absolute   inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-900"
                >
                  <FontAwesomeIcon icon={faTimes} />
                </button>
              )}
            </div>
            <Select
              allowClear
              placeholder="Tất cả vai trò"
              value={selectedRole ?? undefined}
              onChange={(value) => {
                setSelectedRole(value || null);
                setCurrentPage(1);
                fetchUsers();
              }}
              options={[
                { label: "Tất cả vai trò", value: null },
                ...allRoles.map((role) => ({
                  label: role.name,
                  value: role.id,
                })),
              ]}
            />

            <Select
              allowClear
              placeholder="Tất cả trạng thái"
              value={selectedStatus ?? undefined}
              onChange={(value) => {
                setSelectedStatus(value || null);
                setCurrentPage(1);
                fetchUsers();
              }}
              options={[
                { label: "Tất cả trạng thái", value: null },
                { label: "Hoạt động", value: "true" },
                { label: "Không hoạt động", value: "false" },
              ]}
            />
            <Select
              allowClear
              placeholder="Tất cả trường"
              value={selectedOrganization ?? undefined}
              onChange={(value) => {
                setSelectedOrganization(value || null);
                setCurrentPage(1);
                fetchUsers();
              }}
              options={[
                { label: "Tất cả trường", value: null },
                ...organizationOptions.map((org) => ({
                  label: org.name,
                  value: org.id,
                })),
              ]}
            />
            <Select
              allowClear
              placeholder="Tất cả lớp"
              value={selectedClass ?? undefined}
              onChange={(value) => {
                setSelectedClass(value || null);
                setCurrentPage(1);
                fetchUsers();
              }}
              options={[
                { label: "Tất cả lớp", value: null },
                ...allClasses.map((cls) => ({
                  label: cls.name,
                  value: cls.id.toString(),
                })),
              ]}
            />
          </form>
        </section>

        <section className="max-w-full rounded-lg bg-gray-50 p-6">
          <div className="mb-4 flex justify-end">
            <Button
              type="primary"
              className="inline-flex items-center gap-2 rounded-full bg-red-600 px-5 py-3 text-base font-semibold text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
              icon={<FontAwesomeIcon icon={faPlus} className="text-lg" />}
              onClick={() => openModal()}
            >
              Thêm mới
            </Button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full border-separate border-spacing-y-3 text-left text-sm text-gray-600">
              <thead>
                <tr className="border-b border-gray-200">
                  <th
                    className="w-[120px] pb-3 font-normal"
                    onClick={() => handleSort("fullName")}
                  >
                    <div className="flex items-center gap-1">
                      Họ tên
                      <FontAwesomeIcon
                        icon={
                          orderColumn === "fullName"
                            ? orderDirection === 0
                              ? faSortUp
                              : faSortDown
                            : faSort
                        }
                        className={`text-xs ${orderColumn === "fullName" ? "text-red-600" : "text-gray-400"}`}
                      />
                    </div>
                  </th>
                  <th
                    className="w-[130px] pb-3 font-normal"
                    onClick={() => handleSort("phoneNumber")}
                  >
                    <div className="flex items-center gap-1">
                      Số điện thoại
                      <FontAwesomeIcon
                        icon={
                          orderColumn === "phoneNumber"
                            ? orderDirection === 0
                              ? faSortUp
                              : faSortDown
                            : faSort
                        }
                        className={`text-xs ${orderColumn === "phoneNumber" ? "text-red-600" : "text-gray-400"}`}
                      />
                    </div>
                  </th>
                  <th
                    className="w-[220px] pb-3 font-normal"
                    onClick={() => handleSort("email")}
                  >
                    <div className="flex items-center gap-1">
                      Email
                      <FontAwesomeIcon
                        icon={
                          orderColumn === "email"
                            ? orderDirection === 0
                              ? faSortUp
                              : faSortDown
                            : faSort
                        }
                        className={`text-xs ${orderColumn === "email" ? "text-red-600" : "text-gray-400"}`}
                      />
                    </div>
                  </th>
                  <th
                    className="w-[90px] pb-3 font-normal"
                    onClick={() => handleSort("role")}
                  >
                    <div className="flex items-center gap-1">
                      Vai trò
                      <FontAwesomeIcon
                        icon={
                          orderColumn === "role"
                            ? orderDirection === 0
                              ? faSortUp
                              : faSortDown
                            : faSort
                        }
                        className={`text-xs ${orderColumn === "role" ? "text-red-600" : "text-gray-400"}`}
                      />
                    </div>
                  </th>
                  <th
                    className="w-[140px] pb-3 font-normal"
                    onClick={() => handleSort("organizationId")}
                  >
                    <div className="flex items-center gap-1">
                      Trường/Lớp
                      <FontAwesomeIcon
                        icon={
                          orderColumn === "organizationId"
                            ? orderDirection === 0
                              ? faSortUp
                              : faSortDown
                            : faSort
                        }
                        className={`text-xs ${orderColumn === "organizationId" ? "text-red-600" : "text-gray-400"}`}
                      />
                    </div>
                  </th>
                  <th
                    className="w-[90px] pb-3 font-normal"
                    onClick={() => handleSort("status")}
                  >
                    <div className="flex items-center gap-1">
                      Trạng thái
                      <FontAwesomeIcon
                        icon={
                          orderColumn === "status"
                            ? orderDirection === 0
                              ? faSortUp
                              : faSortDown
                            : faSort
                        }
                        className={`text-xs ${orderColumn === "status" ? "text-red-600" : "text-gray-400"}`}
                      />
                    </div>
                  </th>
                  <th
                    className="w-[90px] pb-3 font-normal"
                    onClick={() => handleSort("passwordChanged")}
                  >
                    <div className="flex items-center gap-1">
                      Mật khẩu
                      <FontAwesomeIcon
                        icon={
                          orderColumn === "passwordChanged"
                            ? orderDirection === 0
                              ? faSortUp
                              : faSortDown
                            : faSort
                        }
                        className={`text-xs ${orderColumn === "passwordChanged" ? "text-red-600" : "text-gray-400"}`}
                      />
                    </div>
                  </th>
                  <th
                    className="w-[110px] pb-3 font-normal"
                    onClick={() => handleSort("createdAt")}
                  >
                    <div className="flex items-center gap-1">
                      Ngày tạo
                      <FontAwesomeIcon
                        icon={
                          orderColumn === "createdAt"
                            ? orderDirection === 0
                              ? faSortUp
                              : faSortDown
                            : faSort
                        }
                        className={`text-xs ${orderColumn === "createdAt" ? "text-red-600" : "text-gray-400"}`}
                      />
                    </div>
                  </th>
                  <th
                    className="w-[110px] pb-3 font-normal"
                    onClick={() => handleSort("lastLogin")}
                  >
                    <div className="flex items-center gap-1">
                      Đăng nhập cuối
                      <FontAwesomeIcon
                        icon={
                          orderColumn === "lastLogin"
                            ? orderDirection === 0
                              ? faSortUp
                              : faSortDown
                            : faSort
                        }
                        className={`text-xs ${orderColumn === "lastLogin" ? "text-red-600" : "text-gray-400"}`}
                      />
                    </div>
                  </th>
                  <th className="w-[40px] pb-3 font-normal">Thao tác</th>
                </tr>
              </thead>
              <tbody>
                {data.map((item, index) => (
                  <tr
                    key={item.id}
                    className="border-b border-t border-gray-200 bg-white"
                  >
                    <td className="max-w-[120px] whitespace-pre-line py-4 font-semibold leading-tight">
                      {item.firstName}
                      <br />
                      {item.lastName}
                    </td>
                    <td className="flex items-center gap-2 py-4 font-normal text-gray-900">
                      <FontAwesomeIcon icon={faPhoneAlt} />
                      {item.phoneNumber || "—"}
                    </td>
                    <td className="py-4 font-normal">{item.email || "—"}</td>
                    <td className="py-4">
                      {(item.roles ?? []).join(", ") || "—"}
                    </td>
                    <td className="max-w-[140px] py-4">
                      <div className="font-normal leading-tight">
                        {item.organization?.name || "—"}
                      </div>
                      {item.className && (
                        <div className="mt-1 inline-block rounded bg-gray-100 px-2 py-0.5 text-[10px] font-semibold text-gray-700">
                          {item.className}
                        </div>
                      )}
                    </td>
                    <td className="py-4">
                      <Tag
                        color={
                          item.isEnabled === true
                            ? "green"
                            : item.isEnabled === false
                              ? "gold"
                              : "red"
                        }
                        style={{
                          whiteSpace: "pre-line",
                          padding: "6px 12px",
                          fontSize: "12px",
                        }}
                      >
                        {item.isEnabled === true
                          ? "Hoạt động"
                            : "Không hoạt động"}
                      </Tag>
                    </td>

                    <td className="py-4">
                      <Tag
                        color={item.passwordChanged ? "blue" : "red"}
                        style={{
                          whiteSpace: "pre-line",
                          padding: "6px 12px",
                          fontSize: "12px",
                        }}
                      >
                        {item.passwordChanged ? "Đã đổi" : "Chưa đổi"}
                      </Tag>
                    </td>
                    <td className="whitespace-pre-line py-4 font-normal leading-tight">
                      {new Date(item.createdTime).toLocaleDateString("vi-VN")}
                    </td>
                    <td className="whitespace-pre-line py-4 font-normal leading-tight">
                      {new Date(item.lastLoginTime).toLocaleDateString("vi-VN")}
                    </td>
                    <td className="cursor-pointer py-4 text-center text-gray-600">
                      <Dropdown
                        overlay={
                          <Menu>
                            <Menu.Item
                              key="edit"
                              icon={<FontAwesomeIcon icon={faEdit} />}
                              onClick={() => openModal(item)}
                            >
                              Chỉnh sửa
                            </Menu.Item>
                            <Menu.Item
                              key="delete"
                              icon={<FontAwesomeIcon icon={faTrash} />}
                              danger
                              onClick={() => handleDelete(item)}
                            >
                              Xóa
                            </Menu.Item>
                          </Menu>
                        }
                        trigger={["click"]}
                      >
                        <Button
                          type="text"
                          icon={<FontAwesomeIcon icon={faEllipsisV} />}
                        />
                      </Dropdown>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <Pagination
            currentPage={currentPage}
            pageSize={pageSize}
            totalItems={totalItems}
            onPageChange={(page) => setCurrentPage(page)}
            onPageSizeChange={(size) => {
              setPageSize(size);
              setCurrentPage(1);
            }}
          />
        </section>

        <Modal
          title={editingId ? "Cập nhật User" : "Thêm User"}
          open={isModalOpen}
          onCancel={closeModal}
          onOk={editingId ? handleUpdateSubmit : handleCreateSubmit}
          okText={editingId ? "Cập nhật" : "Thêm mới"}
          width={900}
        >
          <Form layout="vertical" form={antdForm}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="userName"
                  label="Tên đăng nhập"
                  rules={[
                    { required: true, message: "Vui lòng nhập tên đăng nhập" },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="firstName"
                  label="Họ"
                  rules={[{ required: true, message: "Vui lòng nhập họ" }]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="lastName"
                  label="Tên"
                  rules={[{ required: true, message: "Vui lòng nhập tên" }]}
                >
                  <Input />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="email"
                  label="Email"
                  rules={[{ required: true, message: "Vui lòng nhập email" }]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="phoneNumber"
                  label="Số điện thoại"
                  rules={[
                    { required: true, message: "Vui lòng nhập số điện thoại" },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="password"
                  label="Mật khẩu"
                  rules={[
                    { required: !editingId, message: "Vui lòng nhập mật khẩu" },
                  ]}
                >
                  <Input.Password />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="address" label="Địa chỉ">
                  <Input />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="organizationId"
                  label="Tổ chức"
                  rules={[{ required: true, message: "Vui lòng chọn tổ chức" }]}
                >
                  <Select
                    options={organizationOptions.map((org) => ({
                      label: org.name,
                      value: org.id,
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="classId" label="Lớp">
                  <Select
                    options={allClasses.map((cls) => ({
                      label: cls.name,
                      value: cls.id,
                    }))}
                  />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item
                  name="roleNames"
                  label="Quyền"
                  rules={[{ required: true, message: "Vui lòng chọn quyền" }]}
                >
                  <Select
                    mode="multiple"
                    placeholder="Chọn quyền người dùng"
                    options={allRoles.map((cls) => ({
                      label: cls.name,
                      value: cls.name,
                    }))}
                  />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item name="imageUrl" label="Image URL">
                  <Input />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
      </div>
    </AdminLayout>
  );
}
