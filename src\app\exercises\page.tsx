"use client";

import React, { useEffect, useState } from "react";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faRedo,
  faSearch,
  faEdit,
  faTrash,
  faTimes,
  faFileExport,
  faDownload,
  faSortUp,
  faSortDown,
  faSort,
} from "@fortawesome/free-solid-svg-icons";
import {
  getExercise,
  deleteExercise,
  saveExercise,
} from "@/services/exercise/exercise";
import { ExerciseSummary } from "@/types/exercise/exercise";
import Pagination from "@/components/common/Pagination";
import { Button, Modal, Form, Input, Select, notification } from "antd";
import { useBreadcrumb } from "@/context/BreadcrumbContext";
import { time } from "node:console";
import { useLoader } from "@/context/LoaderContext";
import * as XLSX from "xlsx";
import AdminLayout from "@/components/Layouts/AdminLayout";
import { getOrganizationById } from "@/services/organization/organization";
import { OrganizationItem } from "@/types/organization/organization";
import { getOrganization } from "@/services/organization/organization";
import ExerciseTracNghiemForm from "./forms/TextInputForm/page";
import Footer from "@/components/layout/Footer";
import { ClassItem } from "@/types/class/class";
import TextInputForm from "./forms/TextInputForm/page";

const initialForm: Partial<ExerciseSummary> = {
  lessonId: 0,
  correctAnswers: "",
  questionData: "",
};

export default function ExercisesPage() {
  const { setLoading } = useLoader();
  const { setBreadcrumb } = useBreadcrumb();
  const [data, setData] = useState<ExerciseSummary[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [inputSearchTerm, setInputSearchTerm] = useState("");
  const [form, setForm] = useState<Partial<ExerciseSummary>>(initialForm);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [antdForm] = Form.useForm();
  const [selectedExercises, setSelectedExercises] = useState<number[]>([]);
  const [currentExerciseType, setCurrentExerciseType] = useState<number | null>(
    null,
  );

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  const [previewImportData, setPreviewImportData] = useState<ExerciseSummary[]>(
    [],
  );
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  const [searchColumn, setSearchColumn] = useState<string | undefined>();
  const [selectedOrganization, setSelectedOrganization] = useState<
    number | null
  >(null);
  const [organizationOptions, setOrganizationOptions] = useState<
    OrganizationItem[]
  >([]);

  const [orderColumn, setOrderColumn] = useState<string | undefined>();
  const [orderDirection, setOrderDirection] = useState<0 | 1 | null>(0); // 0: ASC, 1: DESC

  const [selectedExerciseType, setSelectedExerciseType] = useState<
    number | null
  >(null);

  const fetchExercises = async () => {
    try {
      setLoading(true);

      const res = await getExercise({
        Page: currentPage,
        ItemPerPage: pageSize,
        SearchTerm: searchTerm,
        SearchColumn: searchColumn,
        OrderColumn: orderColumn,
        Direction: orderDirection,
      });

      const items = res.data.data?.items || [];
      const organizationCache = new Map<number, any>();

      const itemsWithOrganizations = await Promise.all(
        items.map(async (Exercise) => {
          if (!organizationCache.has(Exercise.lessonId)) {
            try {
              const organizationRes = await getOrganizationById(
                Exercise.lessonId,
              );
              organizationCache.set(
                Exercise.lessonId,
                organizationRes.data.data,
              );
            } catch (err) {
              console.error(
                `Không lấy được Tổ chức - Trường ID ${Exercise.lessonId}`,
                err,
              );
              organizationCache.set(Exercise.lessonId, null);
            }
          }

          return {
            ...Exercise,
            organization: organizationCache.get(Exercise.lessonId),
          };
        }),
      );

      setData(itemsWithOrganizations);
      setTotalItems(res.data.data?.total || 0);
      setSelectedExercises([]);
    } catch (err) {
      console.error("Lỗi tải danh sách Lớp", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setBreadcrumb("Quản lý chương trình học", "Chương trình học");
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (inputSearchTerm.trim()) {
        setSearchColumn("name");
        setSearchTerm(inputSearchTerm.trim());
      } else {
        setSearchColumn(undefined);
        setSearchTerm("");
      }
      setCurrentPage(1);
    }, 500); // debounce 500ms

    return () => clearTimeout(timeout);
  }, [inputSearchTerm]);

  // Reset currentPage về 1 khi searchTerm thay đổi
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Debounce fetchExercises khi searchTerm, currentPage hoặc pageSize thay đổi
  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchExercises();
    }, 300);

    return () => clearTimeout(timeout);
  }, [searchTerm, currentPage, pageSize, orderColumn, orderDirection]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    const isFilteringByOrg = selectedOrganization !== null;

    setSearchTerm(
      isFilteringByOrg ? String(selectedOrganization) : inputSearchTerm,
    );
    setSearchColumn(isFilteringByOrg ? "lessonId" : undefined);
    setCurrentPage(1);

    setTimeout(() => {
      fetchExercises();
    }, 0);
  };

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const res = await getOrganization({ ItemPerPage: 999 }); // hàm trả về danh sách organizationItem[]
        console.log("res organization", res);
        setOrganizationOptions(res.data?.data?.items ?? []);
      } catch (err) {
        console.error("Lỗi lấy danh sách Tổ chức - Trường", err);
      }
    };
    fetchOrganizations();
  }, []);

  const resetSearch = async () => {
    setSearchTerm("");
    setInputSearchTerm("");
    setSearchColumn(undefined);
    setSelectedOrganization(null);
    setCurrentPage(1);
    await fetchExercises();
  };

  const handleSubmit = async (data: any) => {
    try {
      console.log("📋 Dữ liệu gửi đi:", data);
      const response = await saveExercise(data);
      console.log("✅ Lưu thành công", response.data);
    } catch (error) {
      console.error("❌ Lỗi khi lưu:", error);
    } finally {
      closeModal();
    }
  };
  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: "Bạn có chắc chắn muốn xoá?",
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          await deleteExercise(id);
          notification.success({ message: "Xoá thành công" });
          setCurrentPage(1);
          await fetchExercises();
        } catch {
          notification.error({ message: "Lỗi khi xoá Lớp" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const openTracNghiemModal = (Exercise?: ExerciseSummary) => {
    if (Exercise) {
      setForm(Exercise);
      setEditingId(Exercise.id);
      antdForm.setFieldsValue(Exercise);
    } else {
      setForm(initialForm);
      setEditingId(null);
      antdForm.resetFields();
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setForm(initialForm);
    setEditingId(null);
    setIsModalOpen(false);
  };

  const handleDeleteMultiple = async () => {
    Modal.confirm({
      title: `Bạn có chắc chắn muốn xoá ${selectedExercises.length} Lớp?`,
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          for (const id of selectedExercises) {
            await deleteExercise(id);
          }
          notification.success({ message: "Xoá thành công" });
          setSelectedExercises([]);
          setCurrentPage(1);
          await fetchExercises();
        } catch {
          notification.error({ message: "Lỗi khi xoá nhiều Lớp" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleSort = (column: string) => {
    console.log("column", column);
    console.log("orderColumn", orderColumn);
    if (orderColumn === column) {
      setOrderDirection(orderDirection === 0 ? 1 : 0);
    } else {
      setOrderColumn(column);
      setOrderDirection(0); // default ASC
    }
  };

  const openModal = () => setIsModalOpen(true);

  const renderExerciseFormByType = (typeId: number) => {
    switch (typeId) {
      // case 1:
      //   return <AudioChoiceForm onSubmit={handleSubmit} />;
      case 2:
        return <TextInputForm onSubmit={handleSubmit} />;
      // case 3:
      //   return <SpeakingForm onSubmit={handleSubmit} />;
      // case 4:
      //   return <ImageChoiceForm onSubmit={handleSubmit} />;
      // case 5:
      //   return <TranslationForm onSubmit={handleSubmit} />;
      // case 6:
      //   return <WritingForm onSubmit={handleSubmit} />;
      // case 7:
      //   return <AudioFillForm onSubmit={handleSubmit} />;
      // case 8:
      //   return <PronunciationForm onSubmit={handleSubmit} />;
      // case 9:
      //   return <SentenceOrderingForm onSubmit={handleSubmit} />;
      // case 10:
      //   return <MatchingForm onSubmit={handleSubmit} />;
      default:
        return <div>Loại bài tập không hợp lệ</div>;
    }
  };

  return (
    <AdminLayout>
      <div className="font-sans mx-auto rounded-md bg-white p-6">
        {/* Search + Add Form */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <form
            className="flex w-full flex-wrap items-center gap-2 sm:w-auto"
            onSubmit={handleSearch}
          >
            <Select
              className="min-w-[220px] [&_.ant-select-selector]:rounded-none"
              allowClear
              placeholder="Chọn Tổ chức - Trường"
              value={selectedOrganization ?? undefined}
              onChange={(value) => {
                setSelectedOrganization(value || null);
                setSearchColumn(value ? "lessonId" : undefined);
                setSearchTerm(value ? String(value) : "");
                setCurrentPage(1);
                fetchExercises(); // GỌI HÀM TÌM KIẾM NGAY SAU KHI CHỌN
              }}
              options={organizationOptions.map((org) => ({
                label: org.name,
                value: org.id,
              }))}
            />

            <div className="relative min-w-[220px] max-w-[300px] flex-grow">
              <input
                id="search"
                type="search"
                value={inputSearchTerm}
                onChange={(e) => setInputSearchTerm(e.target.value)}
                placeholder="Tìm kiếm theo tên"
                className="w-full rounded-md border border-gray-300 py-1 pl-10 pr-12 text-gray-700 placeholder-gray-400 focus:border-red-600 focus:outline-none focus:ring-2 focus:ring-red-600"
              />
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-lg text-gray-400">
                <FontAwesomeIcon icon={faSearch} />
              </span>
              <button
                type="reset"
                onClick={resetSearch}
                className="absolute right-0 top-1/2 -translate-y-1/2 px-3 py-1 text-gray-600 hover:text-gray-900"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            <button
              type="submit"
              className="flex items-center justify-center bg-red-600 p-2 px-4 py-2 text-lg text-white hover:bg-red-700"
            >
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </form>

          <div className="flex flex-wrap items-center gap-2">
            {selectedExercises.length > 0 && (
              <Button
                danger
                onClick={handleDeleteMultiple}
                className="rounded-none"
              >
                Xoá đã chọn ({selectedExercises.length})
              </Button>
            )}

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => {
                setSelectedExerciseType(1);
                openModal();
              }}
            >
              Audio Choice Sample
            </Button>

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => {
                setSelectedExerciseType(2);
                openModal();
              }}
            >
              Text Input Sample
            </Button>

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => {
                setSelectedExerciseType(4);
                openModal();
              }}
            >
              Speaking Sample
            </Button>

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => {
                setSelectedExerciseType(5);
                openModal();
              }}
            >
              Matching Sample
            </Button>

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => {
                setSelectedExerciseType(6);
                openModal();
              }}
            >
              Sentence Ordering Sample
            </Button>

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => {
                setSelectedExerciseType(7);
                openModal();
              }}
            >
              Image Choice Sample
            </Button>

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => {
                setSelectedExerciseType(8);
                openModal();
              }}
            >
              Translation Sample
            </Button>

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => {
                setSelectedExerciseType(9);
                openModal();
              }}
            >
              Writing Sample
            </Button>

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => {
                setSelectedExerciseType(10);
                openModal();
              }}
            >
              Pronunciation Sample
            </Button>

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => {
                setSelectedExerciseType(11);
                openModal();
              }}
            >
              Audio Fill Sample
            </Button>
          </div>
        </div>

        {/* Table */}
        <table className="w-full table-auto border-collapse text-sm">
          <thead>
            <tr className="bg-gray-200 font-semibold text-gray-900">
              <th className="w-[60px] border px-2 py-3 text-center">
                <div className="flex items-center justify-center gap-1">
                  <input
                    type="checkbox"
                    checked={
                      selectedExercises.length === data.length &&
                      data.length > 0
                    }
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedExercises(data.map((item) => item.id));
                      } else {
                        setSelectedExercises([]);
                      }
                    }}
                    className="accent-red-600"
                  />
                </div>
              </th>
              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("lessonId")}
              >
                <div className="flex items-center gap-1">
                  Mã bài
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "lessonId"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "lessonId" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>
              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("code")}
              >
                <div className="flex items-center gap-1">
                  Đề bài
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "code"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "code" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>
              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("name")}
              >
                <div className="flex items-center gap-1">
                  Loại bài
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "name"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "name" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>

              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("classId")}
              >
                <div className="flex items-center gap-1">
                  Lớp
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "classId"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "classId" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>

              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("lessonId")}
              >
                <div className="flex items-center gap-1">
                  Tên buổi
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "lessonId"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "lessonId" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>

              <th className="w-[100px] border px-2 py-3 text-right">
                Chức năng
              </th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr
                key={item.id}
                className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-red-50`}
              >
                <td className="w-[60px] border px-2 py-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className="mr-4 text-xs font-bold">
                      {(currentPage - 1) * pageSize + index + 1}
                    </span>
                    <input
                      type="checkbox"
                      checked={selectedExercises.includes(item.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedExercises([...selectedExercises, item.id]);
                        } else {
                          setSelectedExercises(
                            selectedExercises.filter((id) => id !== item.id),
                          );
                        }
                      }}
                      className="accent-red-600"
                    />
                  </div>
                </td>
                <td className="border px-4 py-3 font-semibold">Mã bài</td>
                <td className="border px-4 py-3 font-semibold">Đề bai</td>
                <td className="border px-4 py-3 font-semibold">
                  {item.exerciseType}
                </td>
                <td className="border px-4 py-3">Lớp</td>
                <td className="border px-4 py-3">Tên buổi</td>
                <td className="border px-2 py-3 text-right">
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => openTracNghiemModal(item)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Sửa"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Xoá"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <Pagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={(page) => setCurrentPage(page)}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setCurrentPage(1); // reset về trang 1 khi đổi size
          }}
        />
      </div>
      <Modal
        title={editingId ? "Cập nhật bài tập" : "Thêm bài tập"}
        open={isModalOpen}
        onCancel={closeModal}
        width={1200}
        footer={null}
        destroyOnClose={true}
      >
        <div style={{ maxHeight: "80vh", overflowY: "auto" }}>
          {renderExerciseFormByType(selectedExerciseType || 1)}
        </div>
      </Modal>
    </AdminLayout>
  );
}
