"use client";

import React, { useState, ReactNode } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UploadOutlined,
  UserOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons";
import { Button, Layout, Menu, Drawer, theme } from "antd";
import Breadcrumb from "../Breadcrumb";
import DarkModeSwitcher from "../Header/DarkModeSwitcher";
import DropdownNotification from "../Header/DropdownNotification";
import DropdownMessage from "../Header/DropdownMessage";
import DropdownUser from "../Header/DropdownUser";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faBook,
  faCalendarAlt,
  faCogs,
  faHeadphones,
  faListSquares,
  faSchool,
  faSchoolCircleExclamation,
  faSun,
  faSunPlantWilt,
  faTowerBroadcast,
  faUsersGear,
} from "@fortawesome/free-solid-svg-icons";
import { useRouter, usePathname } from "next/navigation";

const { Header, Sider, Content } = Layout;

interface AdminLayoutProps {
  children: ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const router = useRouter();
  const currentPath = usePathname();
  const [collapsed, setCollapsed] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false); // 👈 responsive state

  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const menuItems = (
    <Menu
      theme="light"
      mode="inline"
      selectedKeys={[currentPath]}
      onClick={({ key }) => {
        setMobileOpen(false);
        router.push(key);
      }}
    >
      <Menu.SubMenu
        key="sub-danh-muc"
        icon={<FontAwesomeIcon icon={faListSquares} />}
        title="Danh mục"
      >
        <Menu.Item key="/organizations" icon={<FontAwesomeIcon icon={faSchool} />}>
          Tổ chức - Trường
        </Menu.Item>
        <Menu.Item key="/classes" icon={<FontAwesomeIcon icon={faUsersGear} />}>
          Lớp
        </Menu.Item>
        <Menu.Item key="/courses" icon={<FontAwesomeIcon icon={faListSquares} />}>
          Môn học
        </Menu.Item>
        <Menu.Item key="/weeks" icon={<FontAwesomeIcon icon={faCalendarAlt} />}>
          Tuần
        </Menu.Item>
        <Menu.Item key="/sessions" icon={<FontAwesomeIcon icon={faSun} />}>
          Buổi
        </Menu.Item>
        <Menu.Item key="/lessions" icon={<FontAwesomeIcon icon={faBook} />}>
          Bài học
        </Menu.Item>
      </Menu.SubMenu>

      <Menu.SubMenu
        key="sub-system"
        icon={<FontAwesomeIcon icon={faCogs} />}
        title="Hệ thống"
      >
        <Menu.Item key="/user">User</Menu.Item>
      </Menu.SubMenu>
    </Menu>

  );

  return (
    <Layout className="min-h-screen">
      {/* Sidebar cho desktop */}
      <Sider
        width={250}
        collapsible
        collapsed={collapsed}
        onCollapse={(value) => setCollapsed(value)}
        theme="light"
        breakpoint="lg"
        className="hidden lg:inline-block p-2"
      >
        <div className="flex h-[100px] items-center justify-center">
          <img src="/LOGO_VIETIQ.png" alt="Logo" className="h-20" />
        </div>
        {menuItems}
      </Sider>

      {/* Drawer cho mobile */}
      <Drawer
        title={
          <div className="flex items-center justify-center">
            <img src="/LOGO_VIETIQ.png" alt="Logo" className="h-12" />
          </div>
        }
        placement="left"
        closable
        onClose={() => setMobileOpen(false)}
        open={mobileOpen}
        bodyStyle={{ padding: 0 }}
      >
        {menuItems}
      </Drawer>

      <Layout>
        {/* Header */}
        <Header className="sticky top-0 z-50 flex w-full items-center justify-between bg-white px-4 py-2 shadow-sm">
          <Button
            type="text"
            icon={<MenuUnfoldOutlined />}
            className="lg:hidden" // chỉ hiện mobile
            onClick={() => setMobileOpen(true)}
            style={{
              fontSize: "20px",
              width: 48,
              height: 48,
            }}
          />
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            className="hidden lg:inline-flex" // chỉ hiện desktop
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: "16px",
              width: 64,
              height: 64,
            }}
          />
          <div className="flex items-center gap-2 sm:gap-4 lg:hidden">
            <Link className="block flex-shrink-0" href="/">
              <Image width={32} height={32} src="/LOGO_VIETIQ.png" alt="Logo" />
            </Link>
          </div>
          <div className="mr-4 flex items-center gap-3">
            <ul className="flex items-center gap-4">
              <div className="hidden items-center space-x-3 sm:flex">
                <FontAwesomeIcon icon={faHeadphones} fontSize={24} />
                <div className="text-gray-600">
                  <div className="text-xs">Liên hệ CSKH</div>
                  <div className="text-sm font-semibold">024 XXXX XXXX</div>
                </div>
              </div>
              {/* <DarkModeSwitcher /> */}
              <DropdownNotification />
              <DropdownMessage />
            </ul>
            <DropdownUser />
          </div>
        </Header>

        {/* Nội dung chính */}
        <Content
          style={{
            margin: "5px 5px",
            padding: 10,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: 0,
          }}
        >
          <Breadcrumb />
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default AdminLayout;
