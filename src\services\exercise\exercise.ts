

import axios from '@/services/axios';
import API_ROUTES from '@/constants/api-routes';
import { ExerciseRequest, ExerciseSummary } from '@/types/exercise/exercise';
import { ApiResponse, PaginationData } from '@/types/common/api-response';
import { QueryParams } from '@/types/common/query-param';

const exercise_API = API_ROUTES.EXERCISE;

export const getExercise = (params: QueryParams) => {
  return axios.get<ApiResponse<PaginationData<ExerciseSummary>>>(exercise_API, { params });
};

export const getExerciseById = (id: number | string) => {
  return axios.get<ApiResponse<ExerciseSummary>>(`${exercise_API}/${id}`);
};

export const saveExercise = (payload: Partial<ExerciseRequest> & { id?: number }) => {
  const formData = new FormData();

  for (const key in payload) {
    if (key === "id") continue; // Không đưa id vào FormData
    const value = payload[key as keyof typeof payload];

    if (value === undefined || value === null) continue;

    if (key === "Items" && typeof value === "object") {
      formData.append("Items", JSON.stringify(value));
    } else {
      formData.append(key, String(value));
    }
  }

  // 🔍 Đảm bảo chắc chắn 2 trường quan trọng này có mặt, kể cả khi rỗng
  if (!formData.has("Metadata")) {
    formData.append("Metadata", "");
  }

  if (!formData.has("QuestionData")) {
    formData.append("QuestionData", "");
  }

  if (payload.id) {
    return axios.put<ApiResponse<ExerciseRequest>>(`${exercise_API}/${payload.id}`, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  }

  return axios.post<ApiResponse<ExerciseRequest>>(exercise_API, formData, {
    headers: { "Content-Type": "multipart/form-data" },
  });
};



export const deleteExercise = (id: number | string) => {
  return axios.delete<ApiResponse<null>>(`${exercise_API}/${id}`);
};
