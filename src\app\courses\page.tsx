"use client";

import React, { useEffect, useState } from "react";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faRedo,
  faSearch,
  faEdit,
  faTrash,
  faTimes,
  faFileExport,
  faFileExcel,
  faDownload,
  faSortUp,
  faSortDown,
  faSort,
} from "@fortawesome/free-solid-svg-icons";
import { getCourse, deleteCourse, saveCourse } from "@/services/course/course";
import { CourseItem } from "@/types/course/course";
import Pagination from "@/components/common/Pagination";
import { Button, Modal, Form, Input, Select, notification } from "antd";
import { useBreadcrumb } from "@/context/BreadcrumbContext";
import { time } from "node:console";
import { useLoader } from "@/context/LoaderContext";
import * as XLSX from "xlsx";
import AdminLayout from "@/components/Layouts/AdminLayout";
import { getClass } from "@/services/class/class"; // ⬅ đảm bảo có hàm này
import { ClassItem } from "@/types/class/class";
import { getClassById } from "@/services/class/class";

const initialForm: Partial<CourseItem> = {
  title: "",
  description: "",
  courseType: "",
  classId: undefined,
};

export default function CoursesPage() {
  const { setLoading } = useLoader();
  const { setBreadcrumb } = useBreadcrumb();
  const [data, setData] = useState<CourseItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [inputSearchTerm, setInputSearchTerm] = useState("");
  const [form, setForm] = useState<Partial<CourseItem>>(initialForm);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [antdForm] = Form.useForm();
  const [selectedCourses, setSelectedCourses] = useState<number[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  const [previewImportData, setPreviewImportData] = useState<CourseItem[]>([]);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [classOptions, setClassOptions] = useState<ClassItem[]>([]);
  const [selectedClassId, setSelectedClassId] = useState<number | null>(null);
  const [sortField, setSortField] = useState<string>("");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [searchColumn, setSearchColumn] = useState<string | undefined>();
  const [orderColumn, setOrderColumn] = useState<string | undefined>();
  const [orderDirection, setOrderDirection] = useState<0 | 1 | null>(0); // 0: ASC, 1: DESC

  const fetchCourses = async () => {
    try {
      setLoading(true);

      const res = await getCourse({
        Page: currentPage,
        ItemPerPage: pageSize,
        SearchTerm: searchTerm,
        SearchColumn: searchColumn,
        OrderColumn: orderColumn,
        Direction: orderDirection,
      });

      const items = res.data.data?.items || [];
      const classCache = new Map<number, any>();

      const itemsWithClass = await Promise.all(
        items.map(async (course) => {
          if (course.classId && !classCache.has(course.classId)) {
            try {
              const classRes = await getClassById(course.classId);
              classCache.set(course.classId, classRes.data.data);
            } catch (err) {
              console.error(`Không tìm được lớp với ID ${course.classId}`, err);
              classCache.set(course.classId, null);
            }
          }
          return {
            ...course,
            class: course.classId ? classCache.get(course.classId) : null,
          };
        }),
      );

      setData(itemsWithClass);
      setTotalItems(res.data.data?.total || 0);
      setSelectedCourses([]);
    } catch (err) {
      console.error("Lỗi tải danh sách môn học", err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    const isFilteringByOrg = selectedClassId !== null;

    setSearchTerm(isFilteringByOrg ? String(selectedClassId) : inputSearchTerm);
    setSearchColumn(isFilteringByOrg ? "classId" : undefined);
    setCurrentPage(1);

    setTimeout(() => {
      fetchCourses();
    }, 0);
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("asc");
    }
  };

  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchCourses();
    }, 300);
    return () => clearTimeout(timeout);
  }, [
    searchTerm,
    currentPage,
    pageSize,
    selectedClassId,
    sortField,
    sortOrder,
  ]);

  useEffect(() => {
    const fetchClasses = async () => {
      try {
        const res = await getClass({ ItemPerPage: 999 });
        setClassOptions(res.data.data?.items ?? []);
      } catch (err) {
        console.error("Lỗi lấy danh sách lớp", err);
      }
    };
    fetchClasses();
    setBreadcrumb("Quản lý môn học", "Danh sách môn học");
  }, []);

  const resetSearch = async () => {
    setSearchTerm("");
    setInputSearchTerm("");
    setSearchColumn(undefined);
    setSelectedClassId(null);
    setCurrentPage(1);
    await fetchCourses();
  };

  const handleSubmit = async () => {
    try {
      const values = await antdForm.validateFields();
      setLoading(true);
      await saveCourse(editingId ? { ...values, id: editingId } : values);
      notification.success({
        message: editingId ? "Cập nhật thành công" : "Thêm mới thành công",
      });
      setCurrentPage(1);
      await fetchCourses();
      closeModal();
    } catch (errorInfo) {
      console.log("Validation Failed:", errorInfo);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: "Bạn có chắc chắn muốn xoá?",
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          await deleteCourse(id);
          notification.success({ message: "Xoá thành công" });
          setCurrentPage(1);
          await fetchCourses();
        } catch {
          notification.error({ message: "Lỗi khi xoá môn học" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const openModal = (course?: CourseItem) => {
    if (course) {
      setForm(course);
      setEditingId(course.id);
      antdForm.setFieldsValue(course); // ✅ Set form field values
    } else {
      setForm(initialForm);
      setEditingId(null);
      antdForm.resetFields(); // ✅ Reset fields
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setForm(initialForm);
    setEditingId(null);
    setIsModalOpen(false);
  };

  const handleDeleteMultiple = async () => {
    Modal.confirm({
      title: `Bạn có chắc chắn muốn xoá ${selectedCourses.length} môn học?`,
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          for (const id of selectedCourses) {
            await deleteCourse(id);
          }
          notification.success({ message: "Xoá thành công" });
          setSelectedCourses([]);
          setCurrentPage(1);
          await fetchCourses();
        } catch {
          notification.error({ message: "Lỗi khi xoá nhiều môn học" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleBulkUpdateType = async (newType: string) => {
    try {
      setLoading(true);
      for (const id of selectedCourses) {
        const item = data.find((i) => i.id === id);
        if (item) {
          await saveCourse({ ...item, courseType: newType });
        }
      }
      notification.success({ message: "Cập nhật loại thành công" });
      setSelectedCourses([]);
      setCurrentPage(1);
      await fetchCourses();
    } catch {
      notification.error({ message: "Cập nhật thất bại" });
    } finally {
      setLoading(false);
    }
  };

  const handleExportExcel = () => {
    const exportData = data.map(({ title, description, courseType }) => ({
      title,
      description,
      courseType,
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "DanhSachMonHoc");

    XLSX.writeFile(workbook, "danh_sach_mon_hoc.xlsx");
  };

  const handleRemovePreviewItem = (index: number) => {
    setPreviewImportData((prev) => prev.filter((_, i) => i !== index));
  };

  useEffect(() => {
  const timeout = setTimeout(() => {
    // Chỉ thực hiện search nếu không lọc theo lớp
    if (!selectedClassId) {
      setSearchTerm(inputSearchTerm);
      setSearchColumn(undefined);
      setCurrentPage(1);
    }
  }, 300);

  return () => clearTimeout(timeout);
}, [inputSearchTerm]);


  return (
    <AdminLayout>
      <div className="font-sans mx-auto rounded-md bg-white p-6">
        {/* Search + Add Form */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <form className="flex w-full sm:w-auto" onSubmit={handleSearch}>
            <Select
              className="min-w-[220px] [&_.ant-select-selector]:rounded-none"
              allowClear
              placeholder="Chọn Tổ chức - Trường"
              value={selectedClassId ?? undefined}
              onChange={(value) => {
                setSelectedClassId(value || null);
                setSearchColumn(value ? "organizationId" : undefined);
                setSearchTerm(value ? String(value) : "");
                setCurrentPage(1);
                fetchCourses(); // GỌI HÀM TÌM KIẾM NGAY SAU KHI CHỌN
              }}
              options={classOptions.map((org) => ({
                label: org.name,
                value: org.id,
              }))}
            />
            <div className="relative flex-grow">
             <input
                id="search"
                type="search"
                value={inputSearchTerm}
                onChange={(e) => setInputSearchTerm(e.target.value)}
                placeholder="Tìm kiếm theo tên"
                className="w-full rounded-md border border-gray-300 py-1 pl-10 pr-12 text-gray-700 placeholder-gray-400 focus:border-red-600 focus:outline-none focus:ring-2 focus:ring-red-600"
              />
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-lg text-gray-400">
                <FontAwesomeIcon icon={faSearch} />
              </span>
              <button
                type="reset"
                onClick={resetSearch}
                className="absolute right-0 top-1/2 -translate-y-1/2 rounded-r-md border-l border-gray-300 bg-white px-3 py-2 text-gray-600 hover:text-gray-900"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            <button
              type="submit"
              className="ml-2 flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </form>

          <div className="flex items-end gap-2">
            {selectedCourses.length > 0 && (
              <>
                <Button
                  danger
                  onClick={handleDeleteMultiple}
                  className="rounded-none"
                >
                  Xoá đã chọn ({selectedCourses.length})
                </Button>
                <Select
                  placeholder="Cập nhật loại môn học"
                  onChange={handleBulkUpdateType}
                  style={{ width: 160, borderRadius: "0" }}
                  className="rounded-none"
                >
                  <Select.Option className="rounded-none" value="Cơ bản">
                    Cơ bản
                  </Select.Option>
                  <Select.Option className="rounded-none" value="Nâng cao">
                    Nâng cao
                  </Select.Option>
                </Select>
              </>
            )}

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => openModal()}
            >
              Thêm môn học
            </Button>

            <Button
              className="rounded-none  bg-success text-white"
              onClick={handleExportExcel}
              icon={<FontAwesomeIcon icon={faDownload} />}
            >
              Xuất Excel
            </Button>
          </div>
        </div>

        {/* Table */}
        <table className="w-full table-auto border-collapse text-sm">
          <thead>
            <tr className="bg-gray-200 font-semibold text-gray-900">
              <th className="w-[60px] border px-2 py-3 text-center">
                <div className="flex items-center justify-center gap-1">
                  <input
                    type="checkbox"
                    checked={selectedCourses.length === data.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedCourses(data.map((item) => item.id));
                      } else {
                        setSelectedCourses([]);
                      }
                    }}
                    className="accent-red-600"
                  />
                </div>
              </th>
              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("classId")}
              >
                <div className="flex items-center gap-1">
                  Lớp
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "classId"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "classId" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>
              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("title")}
              >
                <div className="flex items-center gap-1">
                  Tên môn học
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "title"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "title" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>
              <th className="border px-4 py-3">Mô tả</th>
              <th className="border px-4 py-3">Loại</th>
              <th className="w-[100px] border px-2 py-3 text-right">
                Chức năng
              </th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr
                key={item.id}
                className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-red-50`}
              >
                <td className="w-[60px] border px-2 py-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className="mr-4 text-xs font-bold">
                      {(currentPage - 1) * pageSize + index + 1}
                    </span>
                    <input
                      type="checkbox"
                      checked={selectedCourses.includes(item.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedCourses([...selectedCourses, item.id]);
                        } else {
                          setSelectedCourses(
                            selectedCourses.filter((id) => id !== item.id),
                          );
                        }
                      }}
                      className="accent-red-600"
                    />
                  </div>
                </td>
                <td className="border px-4 py-3 font-semibold">
                  {item.class?.name}
                </td>
                <td className="border px-4 py-3 font-semibold">{item.title}</td>
                <td className="border px-4 py-3">{item.description}</td>
                <td className="border px-4 py-3 font-semibold">
                  {item.courseType}
                </td>
                <td className="border px-2 py-3 text-right">
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => openModal(item)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Sửa"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Xoá"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <Pagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={(page) => setCurrentPage(page)}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setCurrentPage(1); // reset về trang 1 khi đổi size
          }}
        />
      </div>

      <Modal
        title={editingId ? "Cập nhật môn học" : "Thêm môn học"}
        open={isModalOpen}
        onCancel={closeModal}
        onOk={handleSubmit}
        okText={editingId ? "Cập nhật" : "Thêm mới"}
      >
        <Form layout="vertical" form={antdForm}>
          <Form.Item
            label="Lớp"
            name="classId"
            rules={[{ required: true, message: "Vui lòng chọn lớp" }]}
          >
            <Select placeholder="Chọn lớp">
              {classOptions.map((cls) => (
                <Select.Option key={cls.id} value={cls.id}>
                  {cls.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label="Tên môn học"
            name="title"
            rules={[{ required: true, message: "Vui lòng nhập tên môn học" }]}
          >
            <Input placeholder="Nhập tên môn học" />
          </Form.Item>

          <Form.Item
            label="Mô tả"
            name="description"
            rules={[{ required: true, message: "Vui lòng nhập mô tả" }]}
          >
            <Input placeholder="Nhập mô tả" />
          </Form.Item>

          <Form.Item
            label="Loại môn học"
            name="courseType"
            rules={[{ required: true, message: "Vui lòng chọn loại môn học" }]}
          >
            <Select placeholder="Chọn loại môn học">
              <Select.Option value="Cơ bản">Cơ bản</Select.Option>
              <Select.Option value="Nâng cao">Nâng cao</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </AdminLayout>
  );
}
