"use client";

import React, { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faTimes,
  faDownload,
  faSortDown,
  faSort,
  faSortUp
} from "@fortawesome/free-solid-svg-icons";
import Pagination from "@/components/common/Pagination";
import { Button, Modal, Form, Input, Select, notification } from "antd";
import { useBreadcrumb } from "@/context/BreadcrumbContext";
import { useLoader } from "@/context/LoaderContext";
import * as XLSX from "xlsx";
import AdminLayout from "@/components/Layouts/AdminLayout";
import { GradeItem } from "@/types/grade/gradeItem";
import { deleteGrade, getGrade, saveGrade } from "@/services/grade/grade";

const { TextArea } = Input;
const initialForm: Partial<GradeItem> = {
  name: "",
  code: "",
  description: "",
};

export default function GradePage() {
  const { setLoading } = useLoader();
  const { setBreadcrumb } = useBreadcrumb();
  const [data, setData] = useState<GradeItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [inputSearchTerm, setInputSearchTerm] = useState("");
  const [form, setForm] = useState<Partial<GradeItem>>(initialForm);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [antdForm] = Form.useForm();
  const [selectedClasss, setSelectedClasss] = useState<number[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  const [setPreviewImportData] = useState<GradeItem[]>([]);

  const [searchColumn, setSearchColumn] = useState<string | undefined>();

  const [orderColumn, setOrderColumn] = useState<string | undefined>();
  const [orderDirection, setOrderDirection] = useState<0 | 1 | null>(0); // 0: ASC, 1: DESC

  const fetchGrades = async () => {
    try {
      setLoading(true);

      const res = await getGrade({
        Page: currentPage,
        ItemPerPage: pageSize,
        SearchTerm: searchTerm,
        SearchColumn: searchColumn,
        OrderColumn: orderColumn,
        Direction: orderDirection,
      });

      const items = res.data.data?.items || [];

      setData(items);
      setTotalItems(res.data.data?.total || 0);
      setSelectedClasss([]);
    } catch (err) {
      console.error("Lỗi tải danh sách khối lớp", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setBreadcrumb("Quản lý Khối lớp", "Danh sách Khối");
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (inputSearchTerm.trim()) {
        setSearchColumn("name");
        setSearchTerm(inputSearchTerm.trim());
      } else {
        setSearchColumn(undefined);
        setSearchTerm("");
      }
      setCurrentPage(1);
    }, 500); // debounce 500ms

    return () => clearTimeout(timeout);
  }, [inputSearchTerm]);

  // Reset currentPage về 1 khi searchTerm thay đổi
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Debounce fetchGrades khi searchTerm, currentPage hoặc pageSize thay đổi
  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchGrades();
    }, 300);

    return () => clearTimeout(timeout);
  }, [searchTerm, currentPage, pageSize, orderColumn, orderDirection]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    setCurrentPage(1);
    setTimeout(() => {
      fetchGrades();
    }, 0);
  };

  const resetSearch = async () => {
    setSearchTerm("");
    setInputSearchTerm("");
    setSearchColumn(undefined);
    setCurrentPage(1);
    await fetchGrades();
  };

  const handleSubmit = async () => {
    try {
      const values = await antdForm.validateFields();
      setLoading(true);
      await saveGrade(
        editingId
          ? {
            ...values,
            id: editingId,
          }
          : {
            ...values,
          },
      );
      setCurrentPage(1);
      await fetchGrades();
      closeModal();
    } catch (errorInfo) {
      console.log("Validation Failed:", errorInfo);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: "Bạn có chắc chắn muốn xoá?",
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          await deleteGrade(id);
          notification.success({ message: "Xoá thành công" });
          setCurrentPage(1);
          await fetchGrades();
        } catch {
          notification.error({ message: "Lỗi khi xoá" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const openModal = (Grade?: GradeItem) => {
    if (Grade) {
      setForm(Grade);
      setEditingId(Grade.id);
      antdForm.setFieldsValue(Grade);
    } else {
      setForm(initialForm);
      setEditingId(null);
      antdForm.resetFields();
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setForm(initialForm);
    setEditingId(null);
    setIsModalOpen(false);
  };

  const handleDeleteMultiple = async () => {
    Modal.confirm({
      title: `Bạn có chắc chắn muốn xoá ${selectedClasss.length}?`,
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          for (const id of selectedClasss) {
            await deleteGrade(id);
          }
          notification.success({ message: "Xoá thành công" });
          setSelectedClasss([]);
          setCurrentPage(1);
          await fetchGrades();
        } catch {
          notification.error({ message: "Lỗi khi xoá nhiều" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleExportExcel = () => {
    const exportData = data.map(({ code, name, description }) => ({
      code,
      name,
      description,
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "DanhSachKhoi");

    XLSX.writeFile(workbook, "danh_sach_khoi.xlsx");
  };

  const handleSort = (column: string) => {
    console.log("column", column);
    console.log("orderColumn", orderColumn);
    if (orderColumn === column) {
      setOrderDirection(orderDirection === 0 ? 1 : 0);
    } else {
      setOrderColumn(column);
      setOrderDirection(0); // default ASC
    }
  };

  return (
    <AdminLayout>
      <div className="font-sans mx-auto rounded-md bg-white p-6">
        {/* Search + Add Form */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <form
            className="flex w-full flex-wrap items-center gap-2 sm:w-auto"
            onSubmit={handleSearch}
          >
            <div className="relative min-w-[220px] max-w-[300px] flex-grow">
              <input
                id="search"
                type="search"
                value={inputSearchTerm}
                onChange={(e) => setInputSearchTerm(e.target.value)}
                placeholder="Tìm kiếm theo tên"
                className="w-full rounded-md border border-gray-300 py-1 pl-10 pr-12 text-gray-700 placeholder-gray-400 focus:border-red-600 focus:outline-none focus:ring-2 focus:ring-red-600"
              />
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-lg text-gray-400">
                <FontAwesomeIcon icon={faSearch} />
              </span>
              <button
                type="reset"
                onClick={resetSearch}
                className="absolute right-0 top-1/2 -translate-y-1/2 px-3 py-1 text-gray-600 hover:text-gray-900"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            <button
              type="submit"
              className="flex items-center justify-center bg-red-600 p-2 px-4 py-2 text-lg text-white hover:bg-red-700"
            >
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </form>

          <div className="flex flex-wrap items-center gap-2">
            {selectedClasss.length > 0 && (
              <Button
                danger
                onClick={handleDeleteMultiple}
                className="rounded-none"
              >
                Xoá đã chọn ({selectedClasss.length})
              </Button>
            )}

            <Button
              className="rounded-none"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => openModal()}
            >
              Thêm Khối
            </Button>

            <Button
              className="rounded-none bg-success text-white"
              onClick={handleExportExcel}
              icon={<FontAwesomeIcon icon={faDownload} />}
            >
              Xuất Excel
            </Button>
          </div>
        </div>

        {/* Table */}
        <table className="w-full table-auto border-collapse text-sm">
          <thead>
            <tr className="bg-gray-200 font-semibold text-gray-900">
              <th className="w-[60px] border px-2 py-3 text-center">
                <div className="flex items-center justify-center gap-1">
                  <input
                    type="checkbox"
                    checked={
                      selectedClasss.length === data.length && data.length > 0
                    }
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedClasss(data.map((item) => item.id));
                      } else {
                        setSelectedClasss([]);
                      }
                    }}
                    className="accent-red-600"
                  />
                </div>
              </th>
              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("code")}
              >
                <div className="flex items-center gap-1">
                  Mã Khối
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "code"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "code" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>
              <th
                className="cursor-pointer select-none border px-4 py-3"
                onClick={() => handleSort("name")}
              >
                <div className="flex items-center gap-1">
                  Tên Khối
                  <FontAwesomeIcon
                    icon={
                      orderColumn === "name"
                        ? orderDirection === 0
                          ? faSortUp
                          : faSortDown
                        : faSort
                    }
                    className={`text-xs ${orderColumn === "name" ? "text-red-600" : "text-gray-400"}`}
                  />
                </div>
              </th>
              <th className="w-[100px] border px-2 py-3 text-right">
                Chức năng
              </th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr
                key={item.id}
                className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-red-50`}
              >
                <td className="w-[60px] border px-2 py-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className="mr-4 text-xs font-bold">
                      {(currentPage - 1) * pageSize + index + 1}
                    </span>
                    <input
                      type="checkbox"
                      checked={selectedClasss.includes(item.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedClasss([...selectedClasss, item.id]);
                        } else {
                          setSelectedClasss(
                            selectedClasss.filter((id) => id !== item.id),
                          );
                        }
                      }}
                      className="accent-red-600"
                    />
                  </div>
                </td>
                <td className="border px-4 py-3 font-semibold">{item.code}</td>
                <td className="border px-4 py-3 font-semibold">{item.name}</td>
                <td className="border px-2 py-3 text-right">
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => openModal(item)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Sửa"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Xoá"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <Pagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={(page) => setCurrentPage(page)}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setCurrentPage(1); // reset về trang 1 khi đổi size
          }}
        />
      </div>
      <Modal
        title={editingId ? "Cập nhật khối" : "Thêm khối"}
        open={isModalOpen}
        onCancel={closeModal}
        onOk={handleSubmit}
        okText={editingId ? "Cập nhật" : "Thêm mới"}
      >
        <Form layout="vertical" form={antdForm}>
          <Form.Item
            label="Mã khối"
            name="code"
            rules={[{ required: true, message: "Vui lòng nhập mã khối" }]}
          >
            <Input placeholder="Nhập mã khối" />
          </Form.Item>

          <Form.Item
            label="Tên khối"
            name="name"
            rules={[{ required: true, message: "Vui lòng nhập tên khối" }]}
          >
            <Input placeholder="Nhập tên khối" />
          </Form.Item>

          <Form.Item
            label="Description"
            name="description"
          >
            <TextArea placeholder="Nhập mô tả" autoSize={{ minRows: 3, maxRows: 5 }} />
          </Form.Item>
        </Form>
      </Modal>{" "}
    </AdminLayout>
  );
}
