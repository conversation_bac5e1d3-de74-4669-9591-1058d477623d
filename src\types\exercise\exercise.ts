export interface Media {
  type: number;
  displayType: number;
  content: string;
  filePath: string;
  displayOrder: number;
  file: string; // base64 hoặc tên file
}


export interface Answer {
  groupKey: string;
  isCorrect: boolean;
  filePath: string;
  orderIndex: number;
  displayOrder: number;
  content: string;
  explanation: string;
  matchKey: string;
  type: number;
  file: string; // base64 hoặc tên file
}


export interface MatchingPair {
  left: string;
  right: string;
}


export interface Items {
  expectedAnswerText: string;
  medias: Media[];
  questionText: string;
  answers: Answer[];
  explanation: string;
  matchingPairs: MatchingPair[];
  displayType: number;
  displayDirection: number;
  sentenceOrderingJson: string;
}

export interface ExerciseRequest {
  Items: Items; // sẽ được stringify khi gửi qua FormData
  CorrectAnswers: string;
  WeekId: number;
  Metadata: string;
  QuestionData: string;
  LessonId: number;
  ClassId: number;
  TypeId: number;
  Title: string;
}

export interface ExerciseSummary {
  id: number;
  lessonId: number;
  exerciseType: string | null;
  questionData: string | null;
  correctAnswers: string | null;
  metadata: string | null;
}


export enum ExerciseTypeEnum
   {
       AudioChoice = 1,
       TextInput,
       Speaking,
       ImageChoice,
       Translation,
       Writing,
       AudioFill,
       Pronunciation,
       SentenceOrdering,
       Matching,
       TextChoice
   }